package mcp

import (
	"context"
	"reflect"
	"strings"
	"time"
	"unicode"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProjectsService struct{}

// FindProjectByUUID finds a project by its UUID
func (p *ProjectsService) FindProjectByUUID(ctx context.Context, uuid string) (*mcp.Projects, error) {
	var project mcp.Projects
	err := global.GVA_DB.Where("uuid = ?", uuid).
		Where("status = ?", mcp.ProjectStatusCreated).
		Preload("ProjectTools", "is_disabled = 0 OR is_disabled IS NULL").
		First(&project).Error
	if err != nil {
		return nil, err
	}
	return &project, nil
}

// GetAllProjects gets all projects
func (p *ProjectsService) GetAllProjects(ctx context.Context) ([]mcp.Projects, error) {
	var projects []mcp.Projects
	err := global.GVA_DB.Where("status = ?", mcp.ProjectStatusCreated).Find(&projects).Error
	if err != nil {
		return nil, err
	}
	return projects, nil
}

// GetProjectsWithFilters gets projects with various filters
func (p *ProjectsService) GetProjectsWithFilters(ctx context.Context, filter mcpReq.ProjectsSearch) ([]mcp.Projects, error) {
	var projects []mcp.Projects
	db := global.GVA_DB.Model(&mcp.Projects{})

	// Apply filters
	if filter.Status != nil {
		db = db.Where("status = ?", *filter.Status)
	}
	if filter.Name != nil {
		db = db.Where("name LIKE ? OR description LIKE ?",
			"%"+*filter.Name+"%", "%"+*filter.Name+"%")
	}
	if filter.Category != nil {
		db = db.Where("category = ?", *filter.Category)
	}
	if filter.IsOfficial != nil {
		db = db.Where("is_official = ?", *filter.IsOfficial)
	}

	// Apply pagination and ordering
	if filter.PageSize > 0 {
		db = db.Limit(filter.PageSize)
	}
	if filter.Page > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		db = db.Offset(offset)
	}

	// 预加载工具关联
	err := db.Preload("ProjectTools").Find(&projects).Error
	if err != nil {
		return nil, err
	}

	return projects, nil
}

// CreateProjects 创建projects表记录
// Author [yourname](https://github.com/yourname)
func (projectsService *ProjectsService) CreateProjects(ctx context.Context, projects *mcp.Projects) (err error) {
	err = global.GVA_DB.Create(projects).Error
	return err
}

// DeleteProjects 删除projects表记录
// Author [yourname](https://github.com/yourname)
func (projectsService *ProjectsService) DeleteProjects(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&mcp.Projects{}, "id = ?", ID).Error
	return err
}

// DeleteProjectsByIds 批量删除projects表记录
// Author [yourname](https://github.com/yourname)
func (projectsService *ProjectsService) DeleteProjectsByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]mcp.Projects{}, "id in ?", IDs).Error
	return err
}

// UpdateProjects 更新projects表记录
// Author [yourname](https://github.com/yourname)
func (projectsService *ProjectsService) UpdateProjects(ctx context.Context, projects mcp.Projects) (err error) {
	// 将结构体转换为map
	updateMap := make(map[string]interface{})

	// 使用反射获取所有字段
	val := reflect.ValueOf(projects)
	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 跳过基础模型字段
		if fieldType.Anonymous {
			continue
		}

		// 特殊处理时间字段
		if fieldType.Name == "StartTime" || fieldType.Name == "EndTime" {
			timeVal, ok := field.Interface().(time.Time)
			if !ok || timeVal.IsZero() {
				continue // 跳过零值时间字段
			}
		}

		// 如果字段是结构体且不是基本类型，跳过
		if field.Kind() == reflect.Struct && !isBasicType(field.Type()) {
			continue
		}

		// 获取字段名
		columnName := ""
		gormTag := fieldType.Tag.Get("gorm")
		if gormTag != "" {
			// 解析gorm标签
			tags := strings.Split(gormTag, ";")
			for _, tag := range tags {
				if strings.HasPrefix(tag, "column:") {
					columnName = strings.TrimPrefix(tag, "column:")
					break
				}
			}
		}

		// 如果没有找到column标签，尝试使用json标签
		if columnName == "" {
			jsonTag := fieldType.Tag.Get("json")
			if jsonTag != "" {
				if idx := strings.Index(jsonTag, ","); idx != -1 {
					columnName = jsonTag[:idx]
				} else {
					columnName = jsonTag
				}
			}
		}

		// 如果还是没有找到，使用字段名的蛇形命名
		if columnName == "" {
			columnName = toSnakeCase(fieldType.Name)
		}

		// 跳过特殊标签
		if strings.Contains(gormTag, "foreignKey:") || strings.Contains(gormTag, "-") {
			continue
		}

		updateMap[columnName] = field.Interface()
	}

	err = global.GVA_DB.Model(&mcp.Projects{}).Where("id = ?", projects.ID).Updates(updateMap).Error
	return err
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func toSnakeCase(s string) string {
	var result []rune
	for i, r := range s {
		if i > 0 && unicode.IsUpper(r) {
			result = append(result, '_')
		}
		result = append(result, unicode.ToLower(r))
	}
	return string(result)
}

// isBasicType 检查类型是否为基本类型
func isBasicType(t reflect.Type) bool {
	switch t.Kind() {
	case reflect.String, reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16,
		reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint8, reflect.Uint16,
		reflect.Uint32, reflect.Uint64, reflect.Float32, reflect.Float64:
		return true
	case reflect.Struct:
		return t == reflect.TypeOf(time.Time{})
	default:
		return false
	}
}

// GetProjects 根据ID获取projects表记录
// Author [yourname](https://github.com/yourname)
func (projectsService *ProjectsService) GetProjects(ctx context.Context, ID string) (projects mcp.Projects, err error) {
	err = global.GVA_DB.Where("id = ?", ID).Preload("ProjectTools").First(&projects).Error
	return
}

// GetProjectsInfoList 分页获取Projects列表
func (projectsService *ProjectsService) GetProjectsInfoList(ctx context.Context, info mcpReq.ProjectsSearch) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&mcp.Projects{})
	var projectss []mcp.Projects

	// 时间范围筛选
	if info.StartCreatedAt != nil {
		db = db.Where("created_at >= ?", info.StartCreatedAt)
	}
	if info.EndCreatedAt != nil {
		db = db.Where("created_at < ?", info.EndCreatedAt)
	}

	// UUID筛选
	if info.Uuid != nil && *info.Uuid != "" {
		db = db.Where("uuid = ?", *info.Uuid)
	}

	// 名称筛选
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}

	// 状态和分类筛选
	if info.Status != nil && *info.Status != "" {
		db = db.Where("status = ?", *info.Status)
	}
	if info.Category != nil && *info.Category != "" {
		db = db.Where("category = ?", *info.Category)
	}

	// 布尔值筛选
	if info.IsOfficial != nil {
		db = db.Where("is_official = ?", *info.IsOfficial)
	}
	if info.AllowCall != nil {
		db = db.Where("allow_call = ?", *info.AllowCall)
	}
	if info.IsFeatured != nil {
		db = db.Where("is_featured = ?", *info.IsFeatured)
	} else {
		// 固定筛选特色项目
		db = db.Where("is_featured = ?", true)
	}
	// 平台筛选（单值），projects.supported_platforms JSON 中包含该平台；或为空/空数组也纳入
	if info.Platform != nil && *info.Platform != "" {
		// MySQL JSON_CONTAINS on text/json field；为空或'[]'也包含
		db = db.Where("(supported_platforms IS NULL OR supported_platforms = '' OR supported_platforms = '[]' OR JSON_CONTAINS(CAST(supported_platforms AS JSON), ?))", "\""+*info.Platform+"\"")
	}
	// 默认只显示启用的数据
	if info.IsEnabled == nil {
		db = db.Where("is_enabled = ?", true)
	} else {
		db = db.Where("is_enabled = ?", *info.IsEnabled)
	}

	// 长任务开关筛选
	if info.EnableLongTask != nil {
		db = db.Where("enable_long_task = ?", *info.EnableLongTask)
	}

	// 关键词搜索
	if info.Keyword != nil && *info.Keyword != "" {
		global.GVA_LOG.Info("Searching with keyword", zap.String("keyword", *info.Keyword))
		db = db.Where("name LIKE ? OR description LIKE ?",
			"%"+*info.Keyword+"%", "%"+*info.Keyword+"%")
	}

	// 包名和调用方法筛选
	if info.Package != nil && *info.Package != "" {
		db = db.Where("package = ?", *info.Package)
	}
	if info.CallMethod != nil && *info.CallMethod != "" {
		db = db.Where("call_method LIKE ?", "%"+*info.CallMethod+"%")
	}

	// 添加排序规则
	if info.OrderBy != nil {
		switch *info.OrderBy {
		case "latest":
			db = db.Order("created_at DESC")
		case "hot":
			db = db.Order("likes DESC, online_usage_count DESC")
		case "random":
			db = db.Order("RAND()")
		default:
			db = db.Order("created_at DESC")
		}
	} else {
		// 默认按最新排序
		db = db.Order("created_at DESC")
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 分页
	if limit > 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 查询数据
	if info.HaveTools != nil && *info.HaveTools {
		err = db.Preload("ProjectTools").Find(&projectss).Error
	} else {
		err = db.Find(&projectss).Error
	}

	return projectss, total, err
}

// GetAdminProjectsInfoList 管理端分页获取Projects列表（不限制is_enabled）
func (projectsService *ProjectsService) GetAdminProjectsInfoList(ctx context.Context, info mcpReq.ProjectsSearch) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&mcp.Projects{})
	var projectss []mcp.Projects

	// 启用调试模式打印SQL
	db = db.Debug()
	// 时间范围筛选
	if info.StartCreatedAt != nil {
		db = db.Where("created_at >= ?", info.StartCreatedAt)
	}
	if info.EndCreatedAt != nil {
		db = db.Where("created_at < ?", info.EndCreatedAt)
	}

	// UUID筛选
	if info.Uuid != nil && *info.Uuid != "" {
		db = db.Where("uuid = ?", *info.Uuid)
	}

	// 名称筛选
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}

	// 状态和分类筛选
	if info.Status != nil && *info.Status != "" {
		db = db.Where("status = ?", *info.Status)
	}
	if info.Category != nil && *info.Category != "" {
		db = db.Where("category = ?", *info.Category)
	}

	// 布尔值筛选
	if info.IsOfficial != nil {
		db = db.Where("is_official = ?", *info.IsOfficial)
	}
	if info.AllowCall != nil {
		db = db.Where("allow_call = ?", *info.AllowCall)
	}
	if info.IsFeatured != nil {
		db = db.Where("is_featured = ?", *info.IsFeatured)
	}
	// 管理端接口，根据传入参数筛选is_enabled
	if info.IsEnabled != nil {
		db = db.Where("is_enabled = ?", *info.IsEnabled)
	}

	// 长任务开关筛选
	if info.EnableLongTask != nil {
		db = db.Where("enable_long_task = ?", *info.EnableLongTask)
	}

	// 关键词搜索
	if info.Keyword != nil && *info.Keyword != "" {
		global.GVA_LOG.Info("Searching with keyword", zap.String("keyword", *info.Keyword))
		db = db.Where("name LIKE ? OR description LIKE ?",
			"%"+*info.Keyword+"%", "%"+*info.Keyword+"%")
	}

	// 包名和调用方法筛选
	if info.Package != nil && *info.Package != "" {
		db = db.Where("package = ?", *info.Package)
	}
	if info.CallMethod != nil && *info.CallMethod != "" {
		db = db.Where("call_method LIKE ?", "%"+*info.CallMethod+"%")
	}

	// 添加排序规则
	if info.OrderBy != nil {
		switch *info.OrderBy {
		case "latest":
			db = db.Order("created_at DESC")
		case "hot":
			db = db.Order("likes DESC, online_usage_count DESC")
		case "random":
			db = db.Order("RAND()")
		default:
			db = db.Order("created_at DESC")
		}
	} else {
		// 默认按最新排序
		db = db.Order("created_at DESC")
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 分页
	if limit > 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 查询数据
	err = db.Find(&projectss).Error
	return projectss, total, err
}

// GetProjectsWithTools gets projects with tools
func (p *ProjectsService) GetProjectsWithTools(ctx context.Context, filter mcpReq.ProjectsSearch) ([]mcp.Projects, error) {
	var projects []mcp.Projects
	db := global.GVA_DB.Model(&mcp.Projects{})

	// Apply filters
	if filter.Status != nil {
		db = db.Where("status = ?", *filter.Status)
	}
	if filter.Name != nil {
		db = db.Where("name LIKE ? OR description LIKE ?",
			"%"+*filter.Name+"%", "%"+*filter.Name+"%")
	}
	if filter.Category != nil {
		db = db.Where("category = ?", *filter.Category)
	}
	if filter.IsOfficial != nil {
		db = db.Where("is_official = ?", *filter.IsOfficial)
	}

	// Apply pagination and ordering
	if filter.PageSize > 0 {
		db = db.Limit(filter.PageSize)
	}
	if filter.Page > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		db = db.Offset(offset)
	}

	// 预加载工具关联
	err := db.Preload("ProjectTools").Find(&projects).Error
	if err != nil {
		return nil, err
	}

	return projects, nil
}

// ProjectSearchResult 项目搜索结果结构
type ProjectSearchResult struct {
	UUID      string `json:"uuid" gorm:"column:uuid"`
	Name      string `json:"name" gorm:"column:name"`
	ToolNames string `json:"tool_names" gorm:"column:tool_names"`
	LlmRemark string `json:"llm_remark" gorm:"column:llm_remark"`
}

// ProjectsSearch 工具库查询 - 执行自定义SQL查询
func (projectsService *ProjectsService) ProjectsSearch(ctx context.Context, platform *string) ([]ProjectSearchResult, error) {
	var results []ProjectSearchResult

	base := `
        SELECT
            p.uuid,
            p.name,
            p.llm_remark,
            GROUP_CONCAT(pt.c_name SEPARATOR ', ') AS tool_names
        FROM
            projects p
        LEFT JOIN
            project_tools pt ON p.id = pt.project_id AND pt.is_disabled = 0
        WHERE
            p.is_enabled = 1
            AND p.is_featured = 1
            AND p.deleted_at IS NULL
    `
	var err error
	if platform != nil && *platform != "" {
		// 平台筛选：包含该平台，或平台字段为空/空数组，或被双重字符串包裹
		sql := base + " AND (p.supported_platforms IS NULL OR p.supported_platforms = '' OR p.supported_platforms = '[]' OR JSON_CONTAINS(CAST(p.supported_platforms AS JSON), ?) OR JSON_CONTAINS(CAST(JSON_UNQUOTE(CAST(p.supported_platforms AS JSON)) AS JSON), ?)) GROUP BY p.uuid, p.name, p.llm_remark"
		err = global.GVA_DB.Raw(sql, "\""+*platform+"\"", "\""+*platform+"\"").Scan(&results).Error
	} else {
		sql := base + " GROUP BY p.uuid, p.name, p.llm_remark"
		err = global.GVA_DB.Raw(sql).Scan(&results).Error
	}
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (projectsService *ProjectsService) GetProjectsPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// IncrementOnlineUsageCount 增加在线使用次数
func (p *ProjectsService) IncrementOnlineUsageCount(ctx context.Context, projectID string) error {
	return global.GVA_DB.Model(&mcp.Projects{}).
		Where("id = ?", projectID).
		UpdateColumn("online_usage_count", gorm.Expr("online_usage_count + ?", 1)).
		Error
}

// IncrementOfflineUsageCount 增加离线使用次数
func (p *ProjectsService) IncrementOfflineUsageCount(ctx context.Context, projectID string) error {
	return global.GVA_DB.Model(&mcp.Projects{}).
		Where("id = ?", projectID).
		UpdateColumn("offline_usage_count", gorm.Expr("offline_usage_count + ?", 1)).
		Error
}

// LikeProject 点赞项目
func (p *ProjectsService) LikeProject(ctx context.Context, projectID uint, userUUID uuid.UUID) error {
	// 检查是否已经点赞
	var count int64
	err := global.GVA_DB.Model(&mcp.ProjectLikes{}).
		Where("project_id = ? AND user_uuid = ?", projectID, userUUID).
		Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return nil // 已经点赞过，直接返回
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 创建点赞记录
	like := mcp.ProjectLikes{
		ProjectID: projectID,
		UserUUID:  userUUID,
	}
	if err := tx.Create(&like).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新项目点赞数
	if err := tx.Model(&mcp.Projects{}).
		Where("id = ?", projectID).
		UpdateColumn("likes", gorm.Expr("likes + ?", 1)).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// CheckProjectLiked 检查用户是否已点赞项目
func (p *ProjectsService) CheckProjectLiked(ctx context.Context, projectID uint, userUUID uuid.UUID) (bool, error) {
	var count int64
	query := global.GVA_DB.Model(&mcp.ProjectLikes{}).
		Where("project_id = ? AND user_uuid = ?", projectID, userUUID)

	// 打印SQL语句和参数
	sql := global.GVA_DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Model(&mcp.ProjectLikes{}).
			Where("project_id = ? AND user_uuid = ?", projectID, userUUID).
			Count(&count)
	})
	global.GVA_LOG.Info("CheckProjectLiked SQL:",
		zap.String("sql", sql),
		zap.Uint("projectID", projectID),
		zap.String("userUUID", userUUID.String()))

	err := query.Count(&count).Error
	if err != nil {
		global.GVA_LOG.Error("CheckProjectLiked error:",
			zap.Error(err),
			zap.Uint("projectID", projectID),
			zap.String("userUUID", userUUID.String()))
		return false, err
	}

	global.GVA_LOG.Info("CheckProjectLiked result:",
		zap.Int64("count", count),
		zap.Uint("projectID", projectID),
		zap.String("userUUID", userUUID.String()))

	return count > 0, nil
}

// UnlikeProject 取消点赞项目
func (p *ProjectsService) UnlikeProject(ctx context.Context, projectID uint, userUUID uuid.UUID) error {
	// 检查是否已经点赞
	var count int64
	err := global.GVA_DB.Model(&mcp.ProjectLikes{}).
		Where("project_id = ? AND user_uuid = ?", projectID, userUUID).
		Count(&count).Error
	if err != nil {
		return err
	}
	if count == 0 {
		return nil // 没有点赞过，直接返回
	}

	// 开启事务
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 删除点赞记录
	if err := tx.Where("project_id = ? AND user_uuid = ?", projectID, userUUID).
		Delete(&mcp.ProjectLikes{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新项目点赞数
	if err := tx.Model(&mcp.Projects{}).
		Where("id = ?", projectID).
		UpdateColumn("likes", gorm.Expr("likes - ?", 1)).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
